@echo off
echo ================================================================================
echo                    CONTINUOUS P2P TRADING BOT
echo ================================================================================
echo.
echo This bot will run continuously and:
echo  - Monitor competition every 7 seconds (optimal frequency)
echo  - Update prices to beat competition by minimal margins
echo  - Alert if profit ^< 200 KES per 50K transaction
echo  - Keep both ads optimally positioned
echo  - Use floating prices for Bitcoin protection
echo.
echo Press Ctrl+C to stop the bot
echo.
echo ================================================================================
echo Starting bot...
echo ================================================================================
echo.

python enhanced_continuous_bot.py

echo.
echo ================================================================================
echo Bot has stopped
echo ================================================================================
pause
