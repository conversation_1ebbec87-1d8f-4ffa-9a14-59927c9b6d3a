@echo off
echo ================================================================================
echo                    MULTI-CRYPTOCURRENCY P2P TRADING BOT
echo ================================================================================
echo.
echo This bot will manage 4 cryptocurrency pairs simultaneously:
echo  - Monitor USDT, ETH, WLD, and BNB markets every 7 seconds
echo  - 4 BUY ads: Purchase all cryptos from users (min 5000 KES filter)
echo  - 1 SELL ad: Sell USDT to users (M-PESA Safaricom + min 20K max volume)
echo  - MINIMAL margins: 0.01 KES (e.g., 129.52 -> 129.53)
echo  - Real-time conversion rates and arbitrage analysis
echo  - Auto-convert ETH/WLD/BNB to USDT for selling
echo  - Profit threshold: 200 KES per 50K transaction per crypto
echo.
echo Press Ctrl+C to stop the bot
echo.
echo ================================================================================
echo Starting multi-crypto bot...
echo ================================================================================
echo.

python multi_crypto_p2p_bot.py

echo.
echo ================================================================================
echo Multi-crypto bot has stopped
echo ================================================================================
pause
